const DemirbasIcon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width="51" height="51" viewBox="0 0 51 51" fill="none" stroke="none" {...props}>
<path d="M44.3112 38.816L42.2819 44.1077L40.4594 38.5618C40.0425 37.2936 39.8295 35.9585 39.8296 34.6136V11.1875H45.1431V34.2986C45.1432 35.8483 44.8606 37.383 44.3112 38.816Z" fill="#EFF6FF"/>
<path d="M13.4243 4.75024H35.2031C35.6909 4.75031 36.1587 4.94411 36.5036 5.28901C36.8485 5.63391 37.0423 6.10168 37.0424 6.58945V38.9303C37.0423 39.4181 36.8485 39.8858 36.5036 40.2307C36.1587 40.5756 35.6909 40.7693 35.2031 40.7693H11.8678"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M19.7142 4.75098H29.8618H19.7142Z" fill="#EFF6FF"/>
<path d="M19.7142 4.75098H29.8618"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M10.9127 9.34839V6.58945C10.9127 6.10171 11.1064 5.63394 11.4513 5.28902C11.7961 4.94411 12.2639 4.75031 12.7516 4.75024H19.9552"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M31.6995 25.5961L31.6993 43.4525C31.6993 43.9403 31.5055 44.4081 31.1607 44.753C30.8158 45.0979 30.348 45.2917 29.8603 45.2918H6.98255C6.49474 45.2918 6.02689 45.098 5.68193 44.7531C5.33697 44.4082 5.14313 43.9404 5.14307 43.4525V11.2652C5.14307 10.7774 5.33687 10.3095 5.68184 9.96455C6.02681 9.61958 6.49469 9.42578 6.98255 9.42578H24.5263L31.6995 15.5307V25.5961Z" fill="white"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M24.5542 9.34888V13.6406C24.5542 13.8822 24.6017 14.1214 24.6942 14.3446C24.7866 14.5678 24.9221 14.7706 25.0929 14.9414C25.2637 15.1122 25.4665 15.2477 25.6897 15.3401C25.9129 15.4326 26.1521 15.4801 26.3937 15.4801H31.6047L24.5542 9.34888Z" fill="white"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M43.7041 41.8828L42.3321 45.2144L41.0787 41.6641C40.6908 40.565 40.4927 39.4079 40.4929 38.2424V6.74303C40.4928 6.48137 40.5443 6.22227 40.6444 5.98051C40.7445 5.73875 40.8913 5.51908 41.0763 5.33403C41.2613 5.14898 41.4809 5.00217 41.7226 4.90201C41.9644 4.80184 42.2235 4.75028 42.4851 4.75024C43.0137 4.75024 43.5206 4.9602 43.8943 5.33392C44.268 5.70764 44.478 6.21451 44.478 6.74303V37.97C44.4779 39.3122 44.215 40.6415 43.7041 41.8828Z"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M40.4929 8.7356H44.478V11.1879H40.4929V8.7356Z" fill="white"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.5962 22.9076C13.3776 22.9076 14.011 22.2742 14.011 21.4928C14.011 20.7115 13.3776 20.0781 12.5962 20.0781C11.8149 20.0781 11.1815 20.7115 11.1815 21.4928C11.1815 22.2742 11.8149 22.9076 12.5962 22.9076Z" fill="#EFF6FF"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.5962 28.7118C13.3776 28.7118 14.011 28.0784 14.011 27.297C14.011 26.5157 13.3776 25.8823 12.5962 25.8823C11.8149 25.8823 11.1815 26.5157 11.1815 27.297C11.1815 28.0784 11.8149 28.7118 12.5962 28.7118Z" fill="#EFF6FF"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M12.5962 34.4852C13.3776 34.4852 14.011 33.8518 14.011 33.0705C14.011 32.2892 13.3776 31.6558 12.5962 31.6558C11.8149 31.6558 11.1815 32.2892 11.1815 33.0705C11.1815 33.8518 11.8149 34.4852 12.5962 34.4852Z" fill="#EFF6FF"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16.275 21.6108H23.3258"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M11.0637 16.7056H12.5965"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14.4338 16.7056H15.9666"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M17.809 16.7056H19.3418"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16.275 27.1282H23.3258"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16.275 32.9526H20.2601"  stroke-linecap="round" stroke-linejoin="round"/>
</svg>
    );
};

export default DemirbasIcon;
